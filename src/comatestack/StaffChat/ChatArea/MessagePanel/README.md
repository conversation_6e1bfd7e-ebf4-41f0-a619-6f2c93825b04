# Stage Provider 使用说明

## 概述

StageProvider 是一个 React Context，用于支持用户自定义开发的 stage 组件。它允许开发者注册处理函数来响应 `inPlatform` 类型的 ChatAction。

## 主要功能

1. **Stage Handler 注册**: 允许组件注册处理函数来响应特定 taskId 的 inPlatform 操作
2. **全局 Action 处理**: 当 ChatAction 包含 `inPlatform` actionType 时，自动调用对应的处理函数
3. **参数传递**: 将完整的 ChatAction 对象作为参数传递给处理函数

## 类型定义更新

### ChatAnchor 接口
```typescript
export interface ChatAnchor {
    [key: string]: string | number;
    stageId?: string;
    stepId?: string;
    taskId: string;  // 现在是必需的
    tabId?: string;  // 新增
}
```

### StageBase 接口
```typescript
export interface StageBase {
    id: string;
    name: string;
    messageId: string;
    taskId: string;  // 现在是必需的
    actions?: ChatAction[];
}
```

### StepItem 接口
```typescript
export interface StepItem {
    text: string;
    status: ChatStatus;
    anchor?: ChatAnchor;
    stepId: string;
    taskId: string;  // 新增
}
```

## 使用方法

### 1. 基本使用

```typescript
import {useStageContext} from './StageProvider';
import {ChatAction} from '@/types/staff/element';

const MyStageComponent = ({taskId}: {taskId: string}) => {
    const {registerStageHandler, unregisterStageHandler} = useStageContext();

    useEffect(() => {
        const handleAction = async (action: ChatAction) => {
            // 处理 inPlatform 操作
            console.log('Received action:', action);
            
            // 可以访问所有 action 属性
            const {text, query, anchor, enableConfetti} = action;
            
            // 执行自定义逻辑
            if (query) {
                // 处理查询
            }
            
            if (anchor) {
                // 处理导航
            }
        };

        registerStageHandler(taskId, handleAction);

        return () => {
            unregisterStageHandler(taskId);
        };
    }, [taskId, registerStageHandler, unregisterStageHandler]);

    return <div>My Stage Component</div>;
};
```

### 2. ChatAction 结构示例

```typescript
{
    type: 'actions',
    actions: [
        {
            buttonType: 'primary',
            text: '全部采纳',
            right: false,
            actionTypes: ['inPlatform'],  // 关键：包含 inPlatform
            query: '全部采纳',
            anchor: {
                taskId: 'QWERTYGXCVBN',  // 必需：用于找到对应的处理函数
                stageId: 'abc',
                stepId: 'def',
                tabId: 'aaaa',
            },
            enableConfetti: true,
        },
        {
            buttonType: 'default',
            text: '全部放弃',
            actionTypes: ['inPlatform'],
            callback: 'XXXX',
            anchor: {
                taskId: 'QWERTYGXCVBN',
                stageId: 'xyz',
            },
        },
        {
            buttonType: 'text',
            text: '前往平台页面',
            href: 'https://example.com',  // href 操作会在新页面打开
        },
    ],
}
```

## 工作原理

1. **注册阶段**: Stage 组件使用 `registerStageHandler` 注册处理函数
2. **全局存储**: StageProvider 将所有处理函数存储在全局对象 `window.__stageHandlers__` 中
3. **Action 触发**: 当用户点击包含 `inPlatform` actionType 的按钮时
4. **处理函数查找**: 系统根据 `anchor.taskId` 查找对应的处理函数
5. **函数调用**: 调用处理函数并传递完整的 ChatAction 对象

## 注意事项

1. **taskId 必需**: anchor 中必须包含 taskId，否则无法找到对应的处理函数
2. **处理函数注册**: 确保在组件挂载时注册处理函数，在卸载时取消注册
3. **错误处理**: 处理函数中的错误会被捕获并记录到控制台
4. **异步支持**: 处理函数可以是异步的

## 示例组件

参考 `StageExample.tsx` 文件查看完整的使用示例。
